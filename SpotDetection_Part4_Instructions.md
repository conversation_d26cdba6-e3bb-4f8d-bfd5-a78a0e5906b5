# Part 4: Evaluation and Advanced Techniques Instructions

## Overview

This section covers model evaluation, prediction, and advanced techniques like test-time augmentation and model ensembling. It includes specific considerations for both 2D and 3D data.

## Model Evaluation

### Basic Evaluation

Evaluate the trained model on the validation set:

```python
# Load best model
best_model_path = os.path.join(SAVE_DIR, EXPERIMENT_NAME, 'best_model.pth')
trainer.load_checkpoint('best_model.pth')

# Evaluate on validation set
val_metrics = trainer.validate()

# Print metrics
print("Validation metrics:")
for key, value in val_metrics.items():
    if key.startswith('val_'):
        print(f"{key}: {value:.4f}")
```

### Evaluation Metrics

The evaluation includes multiple metrics:

1. **Pixel-level Metrics**:
   - IoU (Intersection over Union)
   - Dice coefficient
   - Precision, Recall, F1 score

2. **Instance-level Metrics**:
   - Instance IoU
   - Instance Precision, Recall, F1 score
   - Count error
   - Count RMSE

3. **Boundary Metrics**:
   - Boundary IoU
   - Hausdorff distance

### 2D vs 3D Evaluation

For 3D data, consider these adjustments:

```python
# Configure model for 3D evaluation
model.configure_for_validation(fast_mode=True)  # Skip expensive operations during validation

# For very large 3D volumes, evaluate slice by slice
def evaluate_large_3d_volume(model, volume, slice_axis=0):
    """Evaluate large 3D volume slice by slice"""
    predictions = []
    
    # Process each slice
    for i in range(volume.shape[slice_axis]):
        # Extract slice
        if slice_axis == 0:
            slice_data = volume[i:i+1]
        elif slice_axis == 1:
            slice_data = volume[:, i:i+1]
        else:
            slice_data = volume[:, :, i:i+1]
        
        # Predict
        with torch.no_grad():
            outputs = model(slice_data)
        
        # Store prediction
        predictions.append(outputs['heatmap'])
    
    # Combine predictions
    combined_prediction = torch.cat(predictions, dim=slice_axis)
    
    return combined_prediction
```

## Prediction and Visualization

### Basic Prediction

Make predictions on new images:

```python
# Create predictor
predictor = SpotDetectionPredictor(
    model_path=best_model_path,
    device=device,
    threshold=0.5,
    min_size=2,
    min_distance=2
)

# Process an image
outputs = predictor.predict(image)

# Visualize results
predictor.visualize_results(image, outputs)
```

### Batch Processing

Process multiple images:

```python
# Process a directory of images
predictor.process_directory(
    input_dir='/path/to/images',
    save_dir='/path/to/results',
    image_extensions=['.png', '.jpg', '.tif']
)
```

### 2D vs 3D Visualization

For 3D data visualization:

```python
def visualize_3d_results(image, outputs, slice_indices=None):
    """Visualize 3D results at specific slices"""
    # Get volume dimensions
    depth = image.shape[2] if image.dim() == 5 else image.shape[1]
    
    # Default to middle slice if not specified
    if slice_indices is None:
        slice_indices = [depth // 4, depth // 2, 3 * depth // 4]
    
    # Create figure
    fig, axes = plt.subplots(len(slice_indices), 4, figsize=(20, 5*len(slice_indices)))
    
    # For single slice case
    if len(slice_indices) == 1:
        axes = axes.reshape(1, -1)
    
    # Get predictions
    heatmap = torch.sigmoid(outputs['heatmap']).squeeze().cpu().numpy()
    instance_labels = outputs.get('instance_labels')
    if instance_labels is not None:
        instance_labels = instance_labels.squeeze().cpu().numpy()
    
    # Plot each slice
    for i, slice_idx in enumerate(slice_indices):
        # Extract slices
        if image.dim() == 5:  # [B, C, D, H, W]
            image_slice = image[0, 0, slice_idx].cpu().numpy()
        else:  # [C, D, H, W] or [D, H, W]
            if image.dim() == 4:
                image_slice = image[0, slice_idx].cpu().numpy()
            else:
                image_slice = image[slice_idx].cpu().numpy()
        
        heatmap_slice = heatmap[slice_idx]
        instance_slice = instance_labels[slice_idx] if instance_labels is not None else None
        
        # Plot image
        axes[i, 0].imshow(image_slice, cmap='gray')
        axes[i, 0].set_title(f"Slice {slice_idx}")
        axes[i, 0].axis('off')
        
        # Plot heatmap
        axes[i, 1].imshow(heatmap_slice, cmap='hot')
        axes[i, 1].set_title(f"Heatmap {slice_idx}")
        axes[i, 1].axis('off')
        
        # Plot binary segmentation
        binary = (heatmap_slice > 0.5).astype(np.float32)
        axes[i, 2].imshow(binary, cmap='gray')
        axes[i, 2].set_title(f"Binary {slice_idx}")
        axes[i, 2].axis('off')
        
        # Plot instance segmentation
        if instance_slice is not None:
            # Create colormap with random colors for instances
            num_instances = int(np.max(instance_slice))
            colors = np.random.rand(num_instances + 1, 3)
            colors[0] = [0, 0, 0]  # Background is black
            cmap = plt.cm.colors.ListedColormap(colors)
            
            axes[i, 3].imshow(instance_slice, cmap=cmap)
            axes[i, 3].set_title(f"Instances {slice_idx}")
        else:
            axes[i, 3].imshow(np.zeros_like(binary), cmap='gray')
            axes[i, 3].set_title("No Instances")
        axes[i, 3].axis('off')
    
    plt.tight_layout()
    plt.show()
```

## Semi-supervised Learning

### Implementation

The semi-supervised learning approach:

```python
def train_with_semi_supervised_learning(model, loss_fn, train_dataset, val_dataset, 
                                       num_iterations=3, num_epochs_per_iteration=20):
    """Train with semi-supervised learning"""
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE,
        shuffle=True,
        num_workers=NUM_WORKERS,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=NUM_WORKERS,
        pin_memory=True
    )
    
    # Create optimizer and scheduler
    optimizer = optim.AdamW(
        list(model.parameters()) + list(loss_fn.parameters()),
        lr=LEARNING_RATE,
        weight_decay=WEIGHT_DECAY
    )
    
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=num_epochs_per_iteration, eta_min=LEARNING_RATE/100
    )
    
    # Create trainer
    trainer = SparseSpotTrainer(
        model=model,
        loss_fn=loss_fn,
        train_loader=train_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        scheduler=scheduler,
        device=device,
        save_dir=SAVE_DIR,
        experiment_name=f"{EXPERIMENT_NAME}_semi_supervised",
        metrics_threshold=0.5,
        iou_threshold=0.5,
        clip_grad_norm=1.0,
        semi_supervised=True,
        confidence_threshold=0.9,
        update_interval=5
    )
    
    # Train with semi-supervised learning
    for iteration in range(num_iterations):
        print(f"\nSemi-supervised learning iteration {iteration+1}/{num_iterations}")
        
        # Train for a few epochs
        trainer.train(
            num_epochs=num_epochs_per_iteration,
            patience=num_epochs_per_iteration // 2,
            save_freq=num_epochs_per_iteration,
            primary_metric='val_pixel_f1'
        )
        
        # Update dataset with high-confidence predictions
        trainer.update_dataset_with_predictions()
        
        # Evaluate
        val_metrics = trainer.validate()
        print("\nValidation metrics after iteration:")
        for key, value in val_metrics.items():
            if key.startswith('val_'):
                print(f"{key}: {value:.4f}")
    
    return trainer
```

### 2D vs 3D Considerations

For 3D data:

1. **Memory Efficiency**: Use smaller batch size and fewer epochs per iteration
2. **Conservative Threshold**: Start with higher confidence threshold (0.95)
3. **Slice-by-slice Processing**: Consider updating predictions slice by slice for very large volumes

## Advanced Techniques

### Test-time Augmentation (TTA)

TTA improves prediction accuracy by averaging predictions from augmented inputs:

```python
def predict_with_tta(model, image, num_augmentations=8):
    """Predict with test-time augmentation"""
    # Implementation details in the notebook
    # ...
```

For 3D data, use simpler augmentations:

```python
# 3D TTA augmentations
augmentations_3d = [
    A.Compose([ToTensorV2()]),  # Original image
    A.Compose([A.HorizontalFlip(p=1.0), ToTensorV2()]),  # Horizontal flip
    A.Compose([A.VerticalFlip(p=1.0), ToTensorV2()]),  # Vertical flip
    A.Compose([A.Transpose(p=1.0), ToTensorV2()])  # Transpose
]
```

### Model Ensembling

Combine predictions from multiple models:

```python
def create_model_ensemble(num_models=3):
    """Create an ensemble of models"""
    # Implementation details in the notebook
    # ...

def predict_with_ensemble(models, image):
    """Predict with model ensemble"""
    # Implementation details in the notebook
    # ...
```

For 3D data, use fewer models (2-3) to reduce memory usage.

### Hyperparameter Optimization

For optimal performance, consider tuning these hyperparameters:

1. **Learning Rate**: Try values between 1e-4 and 1e-3
2. **Positive Weight**: Try values between 1.0 and 5.0
3. **Focal Gamma**: Try values between 1.0 and 3.0
4. **Confidence Threshold**: Try values between 0.8 and 0.95

Example using grid search:

```python
# Define hyperparameter grid
param_grid = {
    'learning_rate': [1e-4, 5e-4, 1e-3],
    'pos_weight': [1.0, 2.0, 3.0],
    'focal_gamma': [1.0, 2.0, 3.0]
}

# Perform grid search
best_val_f1 = 0
best_params = {}

for lr in param_grid['learning_rate']:
    for pw in param_grid['pos_weight']:
        for fg in param_grid['focal_gamma']:
            # Create model and loss function
            model = EnhancedSpotDetectionModel(**MODEL_CONFIG).to(device)
            
            loss_fn = SparseOptimizedSpotLoss(
                pos_weight=pw,
                focal_gamma=fg,
                **{k: v for k, v in LOSS_CONFIG.items() if k not in ['pos_weight', 'focal_gamma']}
            ).to(device)
            
            # Create optimizer
            optimizer = optim.AdamW(
                list(model.parameters()) + list(loss_fn.parameters()),
                lr=lr,
                weight_decay=WEIGHT_DECAY
            )
            
            # Create trainer and train for a few epochs
            trainer = SparseSpotTrainer(
                model=model,
                loss_fn=loss_fn,
                train_loader=train_loader,
                val_loader=val_loader,
                optimizer=optimizer,
                scheduler=None,
                device=device,
                save_dir=SAVE_DIR,
                experiment_name=f"grid_search_lr{lr}_pw{pw}_fg{fg}",
                metrics_threshold=0.5,
                iou_threshold=0.5,
                clip_grad_norm=1.0
            )
            
            # Train for a few epochs
            trainer.train(num_epochs=10, patience=5, save_freq=10)
            
            # Evaluate
            val_metrics = trainer.validate()
            val_f1 = val_metrics['val_pixel_f1']
            
            # Update best parameters
            if val_f1 > best_val_f1:
                best_val_f1 = val_f1
                best_params = {
                    'learning_rate': lr,
                    'pos_weight': pw,
                    'focal_gamma': fg
                }
                
print(f"Best parameters: {best_params}")
print(f"Best validation F1: {best_val_f1:.4f}")
```

## Conclusion

This notebook provides a comprehensive solution for spot detection in both 2D and 3D images. It handles sparse annotations through semi-supervised learning and includes advanced techniques for improved performance.

Key takeaways:

1. **Mixture of Experts**: Specialized experts for different spot types
2. **Adaptive Loss Function**: Handles varying spot sizes and densities
3. **Semi-supervised Learning**: Leverages high-confidence predictions
4. **Advanced Techniques**: TTA and model ensembling for better results
5. **2D and 3D Support**: Works with both 2D and 3D images

For best results:

1. **Start Simple**: Begin with basic training before enabling advanced features
2. **Monitor Metrics**: Track both pixel-level and instance-level metrics
3. **Iterative Improvement**: Use semi-supervised learning to gradually improve annotations
4. **Hyperparameter Tuning**: Adjust parameters based on your specific dataset