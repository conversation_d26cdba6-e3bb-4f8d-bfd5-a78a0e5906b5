{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup workspace paths\n", "import os\n", "\n", "# Get the current notebook directory\n", "NOTEBOOK_DIR = os.path.dirname(os.path.abspath('__file__'))\n", "print(NOTEBOOK_DIR)\n", "# Create workspace directories\n", "WORKSPACE = {\n", "    'models': os.path.join(NOTEBOOK_DIR, 'models'),\n", "    'logs': os.path.join(NOTEBOOK_DIR, 'logs'),\n", "    'results': os.path.join(NOTEBOOK_DIR, 'results'),\n", "    'tensorboard': os.path.join(NOTEBOOK_DIR, 'tensorboard_logs')\n", "}\n", "\n", "# Create directories if they don't exist\n", "for dir_path in WORKSPACE.values():\n", "    os.makedirs(dir_path, exist_ok=True)\n", "    print(f'Created directory: {dir_path}')\n", "\n", "# Import TensorBoard for visualization\n", "from torch.utils.tensorboard import SummaryWriter\n", "import datetime\n", "\n", "# Create a timestamp for this run\n", "TIMESTAMP = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')\n", "\n", "# Setup paths for this run\n", "RUN_DIR = os.path.join(WORKSPACE['models'], f'run_{TIMESTAMP}')\n", "os.makedirs(RUN_DIR, exist_ok=True)\n", "TENSORBOARD_DIR = os.path.join(WORKSPACE['tensorboard'], f'run_{TIMESTAMP}')\n", "\n", "print(f'\\nWorkspace setup complete. Run directory: {RUN_DIR}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"data\"></a>\n", "## 2. Data Loading and Preparation\n", "\n", "Now let's configure and load our data. We'll set up data loaders for both real and synthetic spot data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration for data loading and processing\n", "DATA_DIR = None  # Set to your data directory if you have real data\n", "IMAGE_SIZE = 256\n", "BATCH_SIZE = 8\n", "USE_SYNTHETIC = True  # Set to False if you only want to use real data\n", "SYNTHETIC_SIZE = 500  # Number of synthetic samples to generate\n", "AUGMENTATION_LEVEL = 'strong'  # 'none', 'light', 'medium', or 'strong'\n", "\n", "# Create data loaders\n", "train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(\n", "    data_dir=DATA_DIR,\n", "    batch_size=BATCH_SIZE,\n", "    image_size=IMAGE_SIZE,\n", "    train_val_split=0.8,\n", "    synthetic=USE_SYNTHETIC,\n", "    synthetic_size=SYNTHETIC_SIZE,\n", "    augmentation_level=AUGMENTATION_LEVEL,\n", "    num_workers=4\n", ")\n", "\n", "print(f\"Created data loaders with {len(train_dataset)} training samples and {len(val_dataset)} validation samples\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Optimized Spot Detection with Mixture of Experts\n", "\n", "This notebook provides a comprehensive guide to training and evaluating the spot detection model with a Mixture of Experts approach. The model is specifically designed to detect spots of varying sizes and densities in images.\n", "\n", "## Key Features\n", "\n", "- **Mixture of Experts Architecture**: Specialized experts for different spot types\n", "- **Optimized Loss Function**: Combines multiple components for better spot detection\n", "- **Adaptive Weighting**: Density-aware and size-adaptive weighting\n", "- **Support for Sparse Annotations**: <PERSON><PERSON> incomplete ground truth\n", "- **Semi-supervised Learning**: Leverages high-confidence predictions to expand training set\n", "- **Data Augmentation**: Comprehensive augmentation pipeline\n", "\n", "## Table of Contents\n", "\n", "1. [Setup and Dependencies](#setup)\n", "2. [Data Loading and Preparation](#data)\n", "3. [Model and Loss Function](#model)\n", "4. [Training](#training)\n", "5. [Evaluation](#evaluation)\n", "6. [Prediction and Visualization](#prediction)\n", "7. [Semi-supervised Learning](#semi-supervised)\n", "8. [Advanced Techniques](#advanced)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"setup\"></a>\n", "## 1. Setup and Dependencies\n", "\n", "First, let's install the required dependencies and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision albumentations scikit-image tqdm matplotlib opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import standard libraries\n", "import os\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader\n", "import albumentations as A\n", "from albumentations.pytorch import ToTensorV2\n", "from tqdm.notebook import tqdm\n", "import cv2\n", "from skimage import io, measure\n", "from scipy.ndimage import binary_erosion\n", "from typing import Dict, List, Tuple, Optional, Union\n", "import random\n", "import time\n", "import json\n", "from scipy.ndimage import binary_erosion\n", "from synthetic_data_generator import AdvancedSyntheticSpotGenerator\n", "\n", "def normalize_orientation(arr):\n", "    \"\"\"Normalize the orientation and values of an input array\"\"\"\n", "    if arr is None:\n", "        return None\n", "    \n", "    # Convert to numpy array if not already\n", "    arr = np.array(arr)\n", "    \n", "    # Ensure array is 2D\n", "    if len(arr.shape) > 2:\n", "        arr = arr.squeeze()\n", "    \n", "    # Normalize values to [0,1] range if not already\n", "    if arr.max() > 1.0 or arr.min() < 0.0:\n", "        arr = (arr - arr.min()) / (arr.max() - arr.min() + 1e-8)\n", "    \n", "    return arr\n", "\n", "def normalize_orientation(img):\n", "    \"\"\"\n", "    Normalize the orientation of an image or confidence map\n", "    \"\"\"\n", "    if img is None:\n", "        return None\n", "        \n", "    # Convert to numpy array if needed\n", "    if not isinstance(img, np.ndarray):\n", "        img = np.array(img)\n", "    \n", "    # Ensure the image is in correct orientation\n", "    # Flip vertically if needed (this addresses the confidence map orientation issue)\n", "    if img.shape[0] < img.shape[1]:  # if width > height\n", "        img = np.rot90(img)\n", "    \n", "    return img\n", "\n", "def normalize_orientation(image):\n", "    \"\"\"\n", "    Normalize the orientation of an image to ensure consistent display\n", "    \"\"\"\n", "    # Ensure image is in correct orientation and shape\n", "    if image.ndim == 2:\n", "        return image\n", "    elif image.ndim == 3 and image.shape[-1] == 1:\n", "        return image.squeeze()\n", "    else:\n", "        return image\n", "\n", "# Set random seed for reproducibility\n", "def set_seed(seed=42):\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.manual_seed(seed)\n", "    if torch.cuda.is_available():\n", "        torch.cuda.manual_seed(seed)\n", "        torch.cuda.manual_seed_all(seed)\n", "        torch.backends.cudnn.deterministic = True\n", "        torch.backends.cudnn.benchmark = False\n", "\n", "set_seed()\n", "\n", "# Check if GPU is available\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import our modules\n", "\n", "from OptimizedSpotDetection_model import OptimizedSpotDetectionModel\n", "from data_utils import SpotDataset, create_data_loaders\n", "from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders\n", "from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator\n", "from fixed_spot_loss import FixedSpotLoss\n", "from fixed_metrics import FixedSpotMetrics\n", "from fixed_trainer import FixedSpotTrainer\n", "from enhanced_spot_loss import EnhancedSpotLoss\n", "from enhanced_metrics import EnhancedSpotMetrics\n", "from improved_trainer import ImprovedSpotTrainer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"model\"></a>\n", "## 3. Model and Loss Function\n", "\n", "Now we'll define our model architecture and loss function for spot detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the model\n", "model = OptimizedSpotDetectionModel(\n", "    in_channels=1,  # Grayscale images\n", "    num_experts=3,  # Number of expert branches\n", "    base_filters=64,  # Base number of filters\n", "    dropout_rate=0.2  # Dropout rate for regularization\n", ")\n", "\n", "# Move model to device\n", "model = model.to(device)\n", "\n", "# Print model summary\n", "print(model)\n", "print(f\"\\nTotal parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define loss function\n", "loss_fn = EnhancedSpotLoss(\n", "    bce_weight=1.0,\n", "    dice_weight=1.0,\n", "    focal_weight=0.5,\n", "    ssim_weight=0.2,\n", "    edge_weight=0.3\n", ")\n", "\n", "# Define optimizer\n", "optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "\n", "# Define learning rate scheduler\n", "scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(\n", "    optimizer, mode='min', factor=0.5, patience=5, verbose=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"training\"></a>\n", "## 4. Training\n", "\n", "Now we'll train our model using the ImprovedSpotTrainer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create trainer\n", "trainer = ImprovedSpotTrainer(\n", "    model=model,\n", "    loss_fn=loss_fn,\n", "    optimizer=optimizer,\n", "    scheduler=scheduler,\n", "    device=device,\n", "    metrics=EnhancedSpotMetrics(),\n", "    tensorboard_dir=TENSORBOARD_DIR\n", ")\n", "\n", "# Train the model\n", "best_model_path = os.path.join(RUN_DIR, 'best_model.pth')\n", "trainer.train(\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    num_epochs=30,\n", "    save_path=best_model_path,\n", "    early_stopping_patience=10\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"evaluation\"></a>\n", "## 5. Evaluation\n", "\n", "Let's evaluate our trained model on the validation set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON>ad the best model\n", "model.load_state_dict(torch.load(best_model_path))\n", "model.eval()\n", "\n", "# Evaluate on validation set\n", "val_metrics = trainer.evaluate(val_loader)\n", "print(\"Validation metrics:\")\n", "for metric_name, metric_value in val_metrics.items():\n", "    print(f\"{metric_name}: {metric_value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"prediction\"></a>\n", "## 6. Prediction and Visualization\n", "\n", "Now let's visualize some predictions from our model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get a batch from validation set\n", "val_batch = next(iter(val_loader))\n", "val_images = val_batch['image'].to(device)\n", "val_masks = val_batch['mask'].to(device)\n", "\n", "# Get predictions\n", "with torch.no_grad():\n", "    outputs = model(val_images)\n", "    if isinstance(outputs, dict):\n", "        predictions = torch.sigmoid(outputs['output'])\n", "    else:\n", "        predictions = torch.sigmoid(outputs)\n", "\n", "# Visualize results\n", "fig, axes = plt.subplots(4, 3, figsize=(15, 20))\n", "\n", "for i in range(4):\n", "    # Get image, mask, and prediction\n", "    image = val_images[i].squeeze().cpu().numpy()\n", "    mask = val_masks[i].squeeze().cpu().numpy()\n", "    pred = predictions[i].squeeze().cpu().numpy()\n", "    \n", "    # Display image\n", "    axes[i, 0].imshow(image, cmap='gray')\n", "    axes[i, 0].set_title(f'Image {i+1}')\n", "    axes[i, 0].axis('off')\n", "    \n", "    # Display ground truth mask\n", "    axes[i, 1].imshow(mask, cmap='hot')\n", "    axes[i, 1].set_title(f'Ground Truth {i+1}')\n", "    axes[i, 1].axis('off')\n", "    \n", "    # Display prediction\n", "    axes[i, 2].imshow(pred, cmap='hot')\n", "    axes[i, 2].set_title(f'Prediction {i+1}')\n", "    axes[i, 2].axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fix Spot Detection Issue\n", "\n", "Let's test different threshold and distance parameters to fix the spot detection issue where a single large circle is detected instead of individual spots."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly\n", "\n", "# Test different parameters to find the best ones\n", "best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)\n", "\n", "# Use the best parameters for future detections\n", "result = detect_peaks_directly(\n", "    test_image, \n", "    heatmap, \n", "    min_distance=best_distance, \n", "    min_intensity=best_intensity\n", ")\n", "\n", "# Display the result\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(131)\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "\n", "plt.subplot(132)\n", "plt.imshow(heatmap, cmap='hot')\n", "plt.title('Heatmap')\n", "plt.axis('off')\n", "\n", "plt.subplot(133)\n", "plt.imshow(result['visualization'])\n", "plt.title(f'Detected Spots: {result[\"num_spots\"]}')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly\n", "\n", "# Test different parameters to find the best ones\n", "best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)\n", "\n", "# Use the best parameters for future detections\n", "result = detect_peaks_directly(\n", "    test_image, \n", "    heatmap, \n", "    min_distance=best_distance, \n", "    min_intensity=best_intensity\n", ")\n", "\n", "# Display the result\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(131)\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "\n", "plt.subplot(132)\n", "plt.imshow(heatmap, cmap='hot')\n", "plt.title('Heatmap')\n", "plt.axis('off')\n", "\n", "plt.subplot(133)\n", "plt.imshow(result['visualization'])\n", "plt.title(f'Detected Spots: {result[\"num_spots\"]}')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the force_spot_separation function\n", "from force_spot_separation import force_spot_separation, visualize_spot_detection_steps\n", "\n", "# Load your model and get a test image\n", "# (Assuming you already have model and test_image loaded)\n", "\n", "# Get prediction heatmap from model\n", "def get_prediction(model, image):\n", "    \"\"\"Get prediction heatmap from model\"\"\"\n", "    # Convert to tensor\n", "    if isinstance(image, np.ndarray):\n", "        if len(image.shape) == 2:\n", "            image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()\n", "        else:\n", "            image_tensor = torch.from_numpy(image).unsqueeze(0).float()\n", "    else:\n", "        image_tensor = image\n", "    \n", "    # Ensure values are in [0, 1]\n", "    if image_tensor.max() > 1.0:\n", "        image_tensor = image_tensor / 255.0\n", "    \n", "    # Move to device\n", "    image_tensor = image_tensor.to(device)\n", "    \n", "    # Get prediction\n", "    with torch.no_grad():\n", "        outputs = model(image_tensor)\n", "        \n", "        # Get main output\n", "        if isinstance(outputs, dict):\n", "            pred = outputs.get('output', outputs.get('combined_output', None))\n", "        else:\n", "            pred = outputs\n", "        \n", "        # Apply sigmoid\n", "        pred_sigmoid = torch.sigmoid(pred)\n", "        \n", "        # Convert to numpy\n", "        heatmap = pred_sigmoid.squeeze().cpu().numpy()\n", "    \n", "    return heatmap\n", "\n", "# Get heatmap\n", "heatmap = get_prediction(model, test_image)\n", "\n", "# Apply force_spot_separation with different parameters\n", "result1 = force_spot_separation(test_image, heatmap, min_distance=5, min_intensity=0.1, min_spot_size=3)\n", "result2 = force_spot_separation(test_image, heatmap, min_distance=3, min_intensity=0.05, min_spot_size=2)\n", "\n", "# Visualize the results\n", "visualize_spot_detection_steps(test_image, heatmap, result1)\n", "print(f\"Detected {result1['num_spots']} spots with min_distance=5, min_intensity=0.1\")\n", "\n", "visualize_spot_detection_steps(test_image, heatmap, result2)\n", "print(f\"Detected {result2['num_spots']} spots with min_distance=3, min_intensity=0.05\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the debugging functions\n", "from debug_binary_mask import debug_binary_mask, fix_spot_detection\n", "\n", "# Run the debugging function\n", "debug_results = debug_binary_mask(test_image, model, device)\n", "\n", "# Apply the fixed spot detection\n", "fixed_results = fix_spot_detection(\n", "    test_image, \n", "    heatmap,\n", "    threshold=debug_results['best_threshold'],\n", "    min_spot_size=3,\n", "    min_distance=debug_results.get('best_watershed_min_distance', 5)\n", ")\n", "\n", "# Display the results\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(131)\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "\n", "plt.subplot(132)\n", "plt.imshow(debug_results['best_binary_mask'], cmap='gray')\n", "plt.title(f'Binary Mask (t={debug_results[\"best_threshold\"]})')\n", "plt.axis('off')\n", "\n", "plt.subplot(133)\n", "plt.imshow(fixed_results['visualization'])\n", "plt.title(f'Fixed Detection: {fixed_results[\"num_spots\"]} spots')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nOptimized Parameters:\")\n", "print(f\"threshold = {debug_results['best_threshold']}\")\n", "print(f\"min_distance = {debug_results.get('best_watershed_min_distance', 5)}\")\n", "print(f\"min_spot_size = 3\")\n", "print(f\"Number of spots detected: {fixed_results['num_spots']}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the debugging functions\n", "from debug_binary_mask import debug_binary_mask, fix_spot_detection\n", "\n", "# Run the debugging function\n", "debug_results = debug_binary_mask(test_image, model, device)\n", "\n", "# Apply the fixed spot detection\n", "fixed_results = fix_spot_detection(\n", "    test_image, \n", "    heatmap,\n", "    threshold=debug_results['best_threshold'],\n", "    min_spot_size=3,\n", "    min_distance=debug_results.get('best_watershed_min_distance', 5)\n", ")\n", "\n", "# Display the results\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(131)\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "\n", "plt.subplot(132)\n", "plt.imshow(debug_results['best_binary_mask'], cmap='gray')\n", "plt.title(f'Binary Mask (t={debug_results[\"best_threshold\"]})')\n", "plt.axis('off')\n", "\n", "plt.subplot(133)\n", "plt.imshow(fixed_results['visualization'])\n", "plt.title(f'Fixed Detection: {fixed_results[\"num_spots\"]} spots')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nOptimized Parameters:\")\n", "print(f\"threshold = {debug_results['best_threshold']}\")\n", "print(f\"min_distance = {debug_results.get('best_watershed_min_distance', 5)}\")\n", "print(f\"min_spot_size = 3\")\n", "print(f\"Number of spots detected: {fixed_results['num_spots']}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the spot detection fix functions\n", "from fix_spot_detection import detect_individual_spots, visualize_spot_detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load a trained model\n", "def load_model(model_path, device):\n", "    \"\"\"Load the trained model from disk\"\"\"\n", "    # Create model instance\n", "    model = OptimizedSpotDetectionModel(\n", "        in_channels=1,\n", "        num_experts=3,\n", "        base_filters=64,\n", "        dropout_rate=0.2\n", "    )\n", "    \n", "    # Load weights\n", "    model.load_state_dict(torch.load(model_path, map_location=device))\n", "    model = model.to(device)\n", "    model.eval()\n", "    \n", "    return model\n", "\n", "# Path to your trained model\n", "model_path = os.path.join(WORKSPACE['models'], 'best_model.pth')  # Update this path if needed\n", "\n", "# Check if model exists\n", "if os.path.exists(model_path):\n", "    model = load_model(model_path, device)\n", "    print(f\"Model loaded from {model_path}\")\n", "else:\n", "    print(f\"Model not found at {model_path}. Please specify the correct path.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load a test image\n", "# First, let's create a synthetic test image if we don't have a real one\n", "def create_test_image():\n", "    \"\"\"Create a synthetic test image with spots\"\"\"\n", "    generator = AdvancedSyntheticSpotGenerator(\n", "        image_size=(256, 256),\n", "        min_spots=20,\n", "        max_spots=30,\n", "        min_radius=2,\n", "        max_radius=5,\n", "        density_factor=8,\n", "        mask_threshold=0.4,\n", "        allow_touching=True,\n", "        shape_variation=0.05,\n", "        add_gradients=True,\n", "        realistic_noise=True\n", "    )\n", "    \n", "    # Generate a single image\n", "    images, masks = generator.generate_batch(1)\n", "    \n", "    # Save the image\n", "    test_image_path = os.path.join(WORKSPACE['results'], 'test_image.png')\n", "    plt.imsave(test_image_path, images[0], cmap='gray')\n", "    \n", "    return test_image_path, images[0]\n", "\n", "# Create or load test image\n", "test_image_path = os.path.join(WORKSPACE['results'], 'test_image.png')\n", "if not os.path.exists(test_image_path):\n", "    test_image_path, test_image = create_test_image()\n", "    print(f\"Created test image at {test_image_path}\")\n", "else:\n", "    test_image = io.imread(test_image_path)\n", "    if len(test_image.shape) == 3 and test_image.shape[2] > 1:\n", "        test_image = cv2.cvtColor(test_image, cv2.COLOR_RGB2GRAY)\n", "    test_image = test_image.astype(np.float32) / 255.0\n", "    print(f\"Loaded test image from {test_image_path}\")\n", "\n", "# Display the test image\n", "plt.figure(figsize=(8, 8))\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Test Image')\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get prediction from model\n", "def get_prediction(model, image):\n", "    \"\"\"Get prediction heatmap from model\"\"\"\n", "    # Convert to tensor\n", "    if isinstance(image, np.ndarray):\n", "        if len(image.shape) == 2:\n", "            image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()\n", "        else:\n", "            image_tensor = torch.from_numpy(image).unsqueeze(0).float()\n", "    else:\n", "        image_tensor = image\n", "    \n", "    # Ensure values are in [0, 1]\n", "    if image_tensor.max() > 1.0:\n", "        image_tensor = image_tensor / 255.0\n", "    \n", "    # Move to device\n", "    image_tensor = image_tensor.to(device)\n", "    \n", "    # Get prediction\n", "    with torch.no_grad():\n", "        outputs = model(image_tensor)\n", "        \n", "        # Get main output\n", "        if isinstance(outputs, dict):\n", "            pred = outputs.get('output', outputs.get('combined_output', None))\n", "        else:\n", "            pred = outputs\n", "        \n", "        # Apply sigmoid\n", "        pred_sigmoid = torch.sigmoid(pred)\n", "        \n", "        # Convert to numpy\n", "        heatmap = pred_sigmoid.squeeze().cpu().numpy()\n", "    \n", "    return heatmap\n", "\n", "# Get prediction for test image\n", "heatmap = get_prediction(model, test_image)\n", "\n", "# Display the heatmap\n", "plt.figure(figsize=(8, 8))\n", "plt.imshow(heatmap, cmap='hot')\n", "plt.title('Prediction Heatmap')\n", "plt.colorbar()\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different threshold values\n", "thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]\n", "min_distances = [3, 5, 7, 10]\n", "\n", "# Create figure for threshold comparison\n", "fig, axes = plt.subplots(len(min_distances), len(thresholds), figsize=(20, 16))\n", "\n", "# Test different combinations\n", "results = {}\n", "\n", "for i, min_distance in enumerate(min_distances):\n", "    for j, threshold in enumerate(thresholds):\n", "        # Detect spots with current parameters\n", "        labeled_mask, num_spots, spot_props = detect_individual_spots(\n", "            heatmap, threshold=threshold, min_spot_size=3, min_distance=min_distance\n", "        )\n", "        \n", "        # Store results\n", "        key = f\"threshold_{threshold}_distance_{min_distance}\"\n", "        results[key] = {\n", "            'num_spots': num_spots,\n", "            'labeled_mask': labeled_mask,\n", "            'spot_props': spot_props\n", "        }\n", "        \n", "        # Create RGB overlay\n", "        rgb_mask = np.zeros((*test_image.shape, 3))\n", "        rgb_mask[..., 0] = test_image  # Red channel\n", "        rgb_mask[..., 1] = test_image  # Green channel\n", "        rgb_mask[..., 2] = test_image  # Blue channel\n", "        \n", "        # Overlay spots\n", "        for prop in spot_props:\n", "            y, x = prop['centroid']\n", "            r = int(np.sqrt(prop['area'] / np.pi))\n", "            \n", "            # Draw circle\n", "            from skimage.draw import circle_perimeter\n", "            rr, cc = circle_perimeter(int(y), int(x), r, shape=test_image.shape)\n", "            rgb_mask[rr, cc, 0] = 1.0  # Red\n", "            rgb_mask[rr, cc, 1] = 0.0  # Green\n", "            rgb_mask[rr, cc, 2] = 0.0  # Blue\n", "        \n", "        # Plot result\n", "        axes[i, j].imshow(rgb_mask)\n", "        axes[i, j].set_title(f'T={threshold}, D={min_distance}, N={num_spots}')\n", "        axes[i, j].axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find the best parameters\n", "best_params = None\n", "max_spots = 0\n", "\n", "for key, value in results.items():\n", "    if value['num_spots'] > max_spots:\n", "        max_spots = value['num_spots']\n", "        best_params = key\n", "\n", "# Extract best parameters\n", "if best_params:\n", "    parts = best_params.split('_')\n", "    best_threshold = float(parts[1])\n", "    best_distance = int(parts[3])\n", "    print(f\"\\nBest parameters: threshold={best_threshold}, min_distance={best_distance}\")\n", "    print(f\"Number of spots detected: {max_spots}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the best result in detail\n", "visualize_spot_detection(\n", "    test_image, heatmap, threshold=best_threshold, \n", "    min_spot_size=3, min_distance=best_distance\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to apply the optimized spot detection to any image\n", "def detect_spots_optimized(image, model, threshold=0.2, min_spot_size=3, min_distance=5):\n", "    \"\"\"Detect spots in an image using optimized parameters\"\"\"\n", "    # Get prediction heatmap\n", "    heatmap = get_prediction(model, image)\n", "    \n", "    # Detect individual spots\n", "    labeled_mask, num_spots, spot_props = detect_individual_spots(\n", "        heatmap, threshold=threshold, min_spot_size=min_spot_size, min_distance=min_distance\n", "    )\n", "    \n", "    # Create visualization\n", "    rgb_mask = np.zeros((*image.shape, 3))\n", "    rgb_mask[..., 0] = image  # Red channel\n", "    rgb_mask[..., 1] = image  # Green channel\n", "    rgb_mask[..., 2] = image  # Blue channel\n", "    \n", "    # Overlay spots\n", "    for prop in spot_props:\n", "        y, x = prop['centroid']\n", "        r = int(np.sqrt(prop['area'] / np.pi))\n", "        \n", "        # Draw circle\n", "        from skimage.draw import circle_perimeter\n", "        rr, cc = circle_perimeter(int(y), int(x), r, shape=image.shape)\n", "        rgb_mask[rr, cc, 0] = 1.0  # Red\n", "        rgb_mask[rr, cc, 1] = 0.0  # Green\n", "        rgb_mask[rr, cc, 2] = 0.0  # Blue\n", "    \n", "    return {\n", "        'heatmap': heatmap,\n", "        'labeled_mask': labeled_mask,\n", "        'num_spots': num_spots,\n", "        'spot_props': spot_props,\n", "        'visualization': rgb_mask\n", "    }\n", "\n", "# Example usage with the best parameters\n", "result = detect_spots_optimized(\n", "    test_image, model, \n", "    threshold=best_threshold, \n", "    min_spot_size=3, \n", "    min_distance=best_distance\n", ")\n", "\n", "# Display the result\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(131)\n", "plt.imshow(test_image, cmap='gray')\n", "plt.title('Original Image')\n", "plt.axis('off')\n", "\n", "plt.subplot(132)\n", "plt.imshow(result['heatmap'], cmap='hot')\n", "plt.title('Heatmap')\n", "plt.axis('off')\n", "\n", "plt.subplot(133)\n", "plt.imshow(result['visualization'])\n", "plt.title(f'Detected Spots: {result[\"num_spots\"]}')\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nOptimized Spot Detection Parameters:\")\n", "print(f\"threshold = {best_threshold}\")\n", "print(f\"min_distance = {best_distance}\")\n", "print(f\"min_spot_size = 3\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}