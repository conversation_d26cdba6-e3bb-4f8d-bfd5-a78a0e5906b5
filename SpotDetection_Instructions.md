# Instructions for Spot Detection Notebook

## Overview

This notebook provides a comprehensive solution for spot detection in both 2D and 3D images using a Mixture of Experts approach. It handles spots of varying sizes and densities, and supports sparse annotations through semi-supervised learning.

## Setup Instructions

1. **Environment Setup**:
   - Ensure you have Python 3.6+ installed
   - Required packages: torch, torchvision, albumentations, scikit-image, tqdm, matplotlib, opencv-python
   - GPU is recommended for faster training

2. **Data Preparation**:
   - For real data: Organize images and masks in a directory structure
     - Option 1: `/data/images/` and `/data/masks/` with matching filenames
     - Option 2: Images and masks in the same directory with masks having `_mask` suffix
   - For sparse annotations: Create confidence masks with `_conf` suffix (optional)
   - Set `DATA_DIR` to your data directory path

3. **Configuration**:
   - Adjust model parameters in `MODEL_CONFIG`
   - Adjust loss function parameters in `LOSS_CONFIG`
   - Set training parameters (batch size, learning rate, etc.)
   - Enable/disable synthetic data generation with `USE_SYNTHETIC`

## Working with 2D Images

The notebook is configured for 2D images by default. For 2D images:

1. Set `is_3d=False` in `MODEL_CONFIG`
2. Use standard 2D data loading (no changes needed)
3. Input shape should be `[B, C, H, W]`

## Working with 3D Images

To work with 3D images:

1. Set `is_3d=True` in `MODEL_CONFIG`
2. Ensure your data loader provides 3D volumes
3. Input shape should be `[B, C, D, H, W]`
4. Consider using smaller batch sizes due to increased memory requirements
5. Enable gradient checkpointing for memory efficiency

## Handling Sparse Annotations

For datasets with sparse annotations:

1. Provide confidence masks if available (with `_conf` suffix)
2. Enable semi-supervised learning in the trainer
3. Set appropriate confidence threshold (default: 0.9)
4. Use multiple iterations of semi-supervised learning for gradual improvement

## Training Process

1. **Initial Training**:
   - Train the model on available annotations
   - Use gradient clipping for stability
   - Monitor validation metrics

2. **Semi-supervised Learning** (if enabled):
   - Generate predictions on training data
   - Filter high-confidence predictions
   - Update training set
   - Continue training with updated annotations

3. **Advanced Techniques**:
   - Test-time augmentation for improved accuracy
   - Model ensembling for better results
   - Expert specialization for different spot types

## Troubleshooting

- **Out of Memory Errors**: Reduce batch size, enable gradient checkpointing
- **Slow Training**: Ensure GPU is being used, reduce image size if necessary
- **Poor Performance on Dense Regions**: Increase weight for Expert 4, enable density-aware weighting
- **Small Spots Not Detected**: Increase weight for Expert 1, enable size-adaptive weighting
- **Touching Spots Not Separated**: Increase boundary loss weight, enable contrastive loss

## Notebook Structure

1. **Part 1**: Setup, dependencies, and data loading
2. **Part 2**: Model and loss function implementation
3. **Part 3**: Training pipeline with gradient clipping and semi-supervised learning
4. **Part 4**: Evaluation, prediction, and advanced techniques

Run the main notebook (`SpotDetection.ipynb`) to execute all parts in sequence.