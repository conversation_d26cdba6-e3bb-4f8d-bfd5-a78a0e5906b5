import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt

class ConvBlock(nn.Module):
    """
    Basic convolutional block with batch normalization and ReLU activation
    """
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, padding=padding)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

class DownBlock(nn.Module):
    """
    Downsampling block with max pooling and double convolution
    """
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.pool = nn.MaxPool2d(2)
        self.conv1 = ConvBlock(in_channels, out_channels)
        self.conv2 = ConvBlock(out_channels, out_channels)
    
    def forward(self, x):
        x = self.pool(x)
        x = self.conv1(x)
        x = self.conv2(x)
        return x

class UpBlock(nn.Module):
    """
    Upsampling block with transposed convolution and double convolution
    """
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        self.conv1 = ConvBlock(in_channels, out_channels)
        self.conv2 = ConvBlock(out_channels, out_channels)
    
    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # Adjust dimensions if needed
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        # Concatenate along channel dimension
        x = torch.cat([x2, x1], dim=1)
        
        x = self.conv1(x)
        x = self.conv2(x)
        return x

class ExpertModule(nn.Module):
    """
    Expert module for specific spot type detection
    """
    def __init__(self, in_channels, mid_channels, out_channels=1):
        super().__init__()
        self.conv1 = ConvBlock(in_channels, mid_channels)
        self.conv2 = ConvBlock(mid_channels, mid_channels)
        self.conv3 = nn.Conv2d(mid_channels, out_channels, kernel_size=1)
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        return x

class GatingNetwork(nn.Module):
    """
    Gating network for mixture of experts
    """
    def __init__(self, in_channels, num_experts):
        super().__init__()
        self.conv1 = ConvBlock(in_channels, in_channels // 2)
        self.conv2 = ConvBlock(in_channels // 2, in_channels // 4)
        self.pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Linear(in_channels // 4, num_experts)
    
    def forward(self, x):
        batch_size = x.size(0)
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.pool(x).view(batch_size, -1)
        x = self.fc(x)
        return F.softmax(x, dim=1)

class OptimizedSpotDetectionModel(nn.Module):
    """
    Optimized spot detection model with mixture of experts
    
    This model uses a U-Net backbone with a mixture of experts approach
    to detect spots of varying sizes and densities.
    """
    def __init__(self, 
                 in_channels=1, 
                 num_experts=3, 
                 base_filters=64,
                 dropout_rate=0.2):
        """
        Initialize the model
        
        Args:
            in_channels: Number of input channels
            num_experts: Number of expert modules
            base_filters: Number of base filters (doubled at each level)
            dropout_rate: Dropout rate for regularization
        """
        super().__init__()
        self.num_experts = num_experts
        self.is_3d = False  # Flag for 3D input handling
        
        # Encoder
        self.inc = nn.Sequential(
            ConvBlock(in_channels, base_filters),
            ConvBlock(base_filters, base_filters)
        )
        self.down1 = DownBlock(base_filters, base_filters * 2)
        self.down2 = DownBlock(base_filters * 2, base_filters * 4)
        self.down3 = DownBlock(base_filters * 4, base_filters * 8)
        self.down4 = DownBlock(base_filters * 8, base_filters * 16)
        
        # Dropout for regularization
        self.dropout = nn.Dropout2d(dropout_rate)
        
        # Decoder
        self.up1 = UpBlock(base_filters * 16, base_filters * 8)
        self.up2 = UpBlock(base_filters * 8, base_filters * 4)
        self.up3 = UpBlock(base_filters * 4, base_filters * 2)
        self.up4 = UpBlock(base_filters * 2, base_filters)
        
        # Expert modules
        self.experts = nn.ModuleList([
            ExpertModule(base_filters, base_filters // 2)
            for _ in range(num_experts)
        ])
        
        # Gating network
        self.gating = GatingNetwork(base_filters, num_experts)
        
        # Final convolution
        self.outc = nn.Conv2d(base_filters, 1, kernel_size=1)
    
    def forward(self, x):
        # Handle 3D input if necessary
        x, is_3d = self._handle_3d_input(x) if len(x.shape) == 5 else (x, False)
        original_shape = x.shape
        
        # Encoder path
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x5 = self.dropout(x5)
        
        # Decoder path
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        
        # Expert outputs and weights
        expert_outputs = [expert(x) for expert in self.experts]
        expert_weights = self.gating(x)
        
        # Combine expert outputs
        combined = torch.zeros_like(expert_outputs[0])
        for i, output in enumerate(expert_outputs):
            weight = expert_weights[:, i].view(-1, 1, 1, 1)
            combined += output * weight
        
        # Final output
        heatmap = self.outc(x) + combined
        
        # Apply sigmoid to get probability map
        heatmap = torch.sigmoid(heatmap)
        
        return heatmap
    
    def _handle_3d_input(self, x: torch.Tensor) -> Tuple[torch.Tensor, bool]:
        """Handle 3D input with consistent orientation"""
        is_3d = len(x.shape) == 5  # [B, C, D, H, W]
        
        if is_3d and not self.is_3d:
            # Convert 3D to batch of 2D by unrolling depth dimension
            B, C, D, H, W = x.shape
            x = x.transpose(1, 2).reshape(B*D, C, H, W)
        elif not is_3d and self.is_3d:
            # Add depth dimension
            x = x.unsqueeze(2)
            
        return x, is_3d
        
    def _restore_3d_output(self, output: Dict[str, torch.Tensor], 
                          original_shape: Tuple[int, ...]) -> Dict[str, torch.Tensor]:
        """Restore 3D output shape with consistent orientation"""
        B, C, D, H, W = original_shape
        
        # Restore each output tensor
        restored = {}
        for k, v in output.items():
            if isinstance(v, torch.Tensor):
                # Reshape back to 3D
                if k == 'heatmap' or k.startswith('expert_'):
                    v = v.view(B, D, -1, H, W).transpose(1, 2)
                restored[k] = v
            elif isinstance(v, list) and all(isinstance(t, torch.Tensor) for t in v):
                # Handle lists of tensors (e.g., expert outputs)
                restored[k] = [t.view(B, D, -1, H, W).transpose(1, 2) for t in v]
            else:
                restored[k] = v
                
        return restored
        
    def visualize_3d_activation_maps(self, x: torch.Tensor, 
                                   slice_indices: Optional[List[int]] = None,
                                   max_slices: int = 4) -> None:
        """Visualize 3D activation maps with consistent orientation"""
        self.eval()
        with torch.no_grad():
            # Get activations
            features = self.encoder(x)
            
            # Get default slice indices if not provided
            if slice_indices is None:
                D = x.shape[2] if len(x.shape) == 5 else x.shape[1]
                step = max(1, D // max_slices)
                slice_indices = list(range(0, D, step))[:max_slices]
            
            # Create figure
            n_rows = len(features) + 2  # features + input + output
            n_cols = len(slice_indices)
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
            
            # Plot input slices
            for i, z in enumerate(slice_indices):
                if len(x.shape) == 5:
                    img = x[0, 0, z].cpu()
                else:
                    img = x[0, z].cpu()
                axes[0, i].imshow(img, cmap='gray')
                axes[0, i].set_title(f'Input {z}')
                axes[0, i].axis('off')
            
            # Plot feature maps
            for row, feat in enumerate(features, 1):
                if len(feat.shape) == 5:  # 3D features
                    feat_viz = feat[0, 0]  # Take first channel
                else:  # 2D features
                    feat_viz = feat[0]  # Take first batch
                
                # Plot each slice
                for i, z in enumerate(slice_indices):
                    if len(feat.shape) == 5:
                        fm = feat_viz[z].cpu()
                    else:
                        fm = feat_viz[i].cpu()
                    axes[row, i].imshow(fm, cmap='hot')
                    axes[row, i].set_title(f'Level {row} - {z}')
                    axes[row, i].axis('off')
            
            # Plot output
            outputs = self(x)
            pred = torch.sigmoid(outputs['heatmap'])
            
            for i, z in enumerate(slice_indices):
                if len(pred.shape) == 5:
                    out = pred[0, 0, z].cpu()
                else:
                    out = pred[0, z].cpu()
                axes[-1, i].imshow(out, cmap='hot')
                axes[-1, i].set_title(f'Output {z}')
                axes[-1, i].axis('off')
            
            plt.tight_layout()
            plt.show()