import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import os
import argparse
import time
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union

# Import the model, loss function, and system
# In a real project, you would use:
# from .model import OptimizedSpotDetectionModel
# from .loss import OptimizedSpotLoss
# from .system import SpotDetectionSystem

# For this example, we'll assume they're imported from the files we created
import sys
sys.path.append('/tmp')

# Import the OptimizedSpotLoss from the file we created earlier
from OptimizedSpotLoss import OptimizedSpotLoss

# Create a simple dataset for demonstration
class SpotDataset(Dataset):
    """
    Dataset for spot detection with synthetic data generation
    
    This dataset generates synthetic images with spots of varying sizes and densities
    for training and testing the spot detection model.
    """
    def __init__(self, 
                size: int = 100, 
                image_size: Tuple[int, int] = (256, 256),
                min_spots: int = 5,
                max_spots: int = 20,
                min_radius: int = 2,
                max_radius: int = 10,
                dense_regions_prob: float = 0.3,
                noise_level: float = 0.1,
                is_train: bool = True):
        """
        Initialize the dataset
        
        Args:
            size: Number of samples in the dataset
            image_size: Size of the images (H, W)
            min_spots: Minimum number of spots per image
            max_spots: Maximum number of spots per image
            min_radius: Minimum spot radius
            max_radius: Maximum spot radius
            dense_regions_prob: Probability of generating dense regions
            noise_level: Level of noise to add to the images
            is_train: Whether this is a training dataset
        """
        self.size = size
        self.image_size = image_size
        self.min_spots = min_spots
        self.max_spots = max_spots
        self.min_radius = min_radius
        self.max_radius = max_radius
        self.dense_regions_prob = dense_regions_prob
        self.noise_level = noise_level
        self.is_train = is_train
        
        # Set random seed for reproducibility in validation/test sets
        self.rng = np.random.RandomState(42 if not is_train else None)
    
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        # Create empty image and mask
        h, w = self.image_size
        image = np.zeros((h, w), dtype=np.float32)
        mask = np.zeros((h, w), dtype=np.float32)
        
        # Determine number of spots
        num_spots = self.rng.randint(self.min_spots, self.max_spots + 1)
        
        # Decide whether to create dense regions
        create_dense = self.rng.random() < self.dense_regions_prob
        
        if create_dense:
            # Create 1-3 dense regions with many spots
            num_dense_regions = self.rng.randint(1, 4)
            for _ in range(num_dense_regions):
                # Define dense region center and size
                center_x = self.rng.randint(w // 4, 3 * w // 4)
                center_y = self.rng.randint(h // 4, 3 * h // 4)
                region_size = self.rng.randint(30, 80)
                
                # Add spots in the dense region
                num_dense_spots = self.rng.randint(10, 30)
                for _ in range(num_dense_spots):
                    # Random position within the dense region
                    offset_x = self.rng.randint(-region_size // 2, region_size // 2)
                    offset_y = self.rng.randint(-region_size // 2, region_size // 2)
                    x = center_x + offset_x
                    y = center_y + offset_y
                    
                    # Ensure within image bounds
                    x = max(self.max_radius, min(w - self.max_radius, x))
                    y = max(self.max_radius, min(h - self.max_radius, y))
                    
                    # Smaller spots in dense regions
                    r = self.rng.randint(self.min_radius, self.max_radius // 2 + 1)
                    
                    # Add spot to mask
                    self._add_spot(mask, x, y, r)
                    
                    # Add spot to image with intensity variation
                    intensity = 0.5 + 0.5 * self.rng.random()
                    self._add_spot(image, x, y, r, intensity=intensity)
        
        # Add remaining spots randomly across the image
        remaining_spots = max(0, num_spots - (20 if create_dense else 0))
        for _ in range(remaining_spots):
            # Random position
            x = self.rng.randint(self.max_radius, w - self.max_radius)
            y = self.rng.randint(self.max_radius, h - self.max_radius)
            
            # Random radius
            r = self.rng.randint(self.min_radius, self.max_radius + 1)
            
            # Add spot to mask
            self._add_spot(mask, x, y, r)
            
            # Add spot to image with intensity variation
            intensity = 0.5 + 0.5 * self.rng.random()
            self._add_spot(image, x, y, r, intensity=intensity)
        
        # Add noise to image
        image += self.rng.normal(0, self.noise_level, image.shape)
        
        # Normalize image to [0, 1]
        image = np.clip(image, 0, 1)
        
        # Convert to torch tensors
        image_tensor = torch.from_numpy(image).unsqueeze(0)  # Add channel dimension
        mask_tensor = torch.from_numpy(mask).unsqueeze(0)    # Add channel dimension
        
        return {'image': image_tensor, 'mask': mask_tensor}
    
    def _add_spot(self, array, x, y, r, intensity=1.0):
        """Add a spot to the array at position (x, y) with radius r"""
        h, w = array.shape
        for i in range(max(0, y-r), min(h, y+r+1)):
            for j in range(max(0, x-r), min(w, x+r+1)):
                # Calculate distance from center
                d = np.sqrt((i - y)**2 + (j - x)**2)
                if d <= r:
                    # Gaussian falloff for more realistic spots
                    value = intensity * np.exp(-(d/r)**2)
                    array[i, j] = max(array[i, j], value)


def visualize_results(image, mask, pred_heatmap, instance_labels=None):
    """
    Visualize the results of spot detection
    
    Args:
        image: Input image tensor
        mask: Ground truth mask tensor
        pred_heatmap: Predicted heatmap tensor
        instance_labels: Instance segmentation labels tensor
    """
    # Convert tensors to numpy arrays with consistent orientation
    image_np = image.squeeze().cpu().numpy()
    mask_np = mask.squeeze().cpu().numpy()
    pred_heatmap_np = torch.sigmoid(pred_heatmap).squeeze().cpu().numpy()
    
    fig, axes = plt.subplots(1, 4 if instance_labels is not None else 3, figsize=(16, 4))
    
    # Plot input image
    axes[0].imshow(image_np, cmap='gray', origin='lower')
    axes[0].set_title('Input Image')
    axes[0].axis('off')
    
    # Plot ground truth mask
    axes[1].imshow(mask_np, cmap='hot', origin='lower')
    axes[1].set_title('Ground Truth')
    axes[1].axis('off')
    
    # Plot predicted heatmap
    axes[2].imshow(pred_heatmap_np, cmap='hot')  # Removed origin='lower'
    axes[2].set_title('Predicted Heatmap')
    axes[2].axis('off')
    
    # Plot instance segmentation if available
    if instance_labels is not None:
        instance_np = instance_labels.squeeze().cpu().numpy()
        from matplotlib.colors import ListedColormap
        
        # Create colormap with random colors for instances
        num_instances = int(instance_np.max())
        colors = np.random.rand(num_instances + 1, 3)
        colors[0] = [0, 0, 0]  # Background is black
        cmap = ListedColormap(colors)
        
        axes[3].imshow(instance_np, cmap=cmap)  # Removed origin='lower'
        axes[3].set_title(f'Instance Segmentation ({num_instances} spots)')
        axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('/tmp/spot_detection_results.png')
    plt.close()
    
    print(f"Results visualization saved to /tmp/spot_detection_results.png")


def main():
    """Main function for training and evaluating the spot detection model"""
    parser = argparse.ArgumentParser(description='Train and evaluate spot detection model')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--num_experts', type=int, default=4, help='Number of experts')
    parser.add_argument('--base_filters', type=int, default=32, help='Number of base filters')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout rate')
    parser.add_argument('--save_dir', type=str, default='/tmp/checkpoints', help='Directory to save checkpoints')
    parser.add_argument('--no_cuda', action='store_true', help='Disable CUDA')
    args = parser.parse_args()
    
    # Create save directory
    os.makedirs(args.save_dir, exist_ok=True)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() and not args.no_cuda else 'cpu')
    print(f"Using device: {device}")
    
    # Create datasets
    train_dataset = SpotDataset(size=200, is_train=True)
    val_dataset = SpotDataset(size=50, is_train=False)
    
    # Create dataloaders
    train_dataloader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_dataloader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
    
    # Create model and loss function
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        base_filters=args.base_filters,
        num_experts=args.num_experts,
        dropout=args.dropout
    ).to(device)
    
    loss_fn = OptimizedSpotLoss(
        heatmap_weight=0.5,
        boundary_weight=0.2,
        distance_weight=0.2,
        moe_weight=0.1,
        learn_weights=True,
        density_aware_weighting=True,
        size_adaptive_weighting=True,
        num_experts=args.num_experts
    ).to(device)
    
    # Create optimizer and scheduler
    optimizer = optim.AdamW(
        list(model.parameters()) + list(loss_fn.parameters()),
        lr=args.lr,
        weight_decay=args.weight_decay
    )
    
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=args.epochs, eta_min=args.lr/100
    )
    
    # Training loop
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        print(f"Epoch {epoch+1}/{args.epochs}")
        
        # Training
        model.train()
        train_loss = 0.0
        start_time = time.time()
        
        for batch_idx, batch in enumerate(train_dataloader):
            # Get inputs and targets
            inputs = batch['image'].to(device)
            targets = {
                'masks': batch['mask'].to(device)
            }
            
            # Zero gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            
            # Compute loss
            loss, _ = loss_fn(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Update weights
            optimizer.step()
            
            # Update metrics
            train_loss += loss.item()
            
            # Print progress
            if (batch_idx + 1) % 10 == 0:
                print(f"Batch {batch_idx+1}/{len(train_dataloader)}, Loss: {loss.item():.4f}")
        
        # Compute average training loss
        train_loss /= len(train_dataloader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch in val_dataloader:
                # Get inputs and targets
                inputs = batch['image'].to(device)
                targets = {
                    'masks': batch['mask'].to(device)
                }
                
                # Forward pass
                outputs = model(inputs)
                
                # Compute loss
                loss, _ = loss_fn(outputs, targets)
                
                # Update metrics
                val_loss += loss.item()
        
        # Compute average validation loss
        val_loss /= len(val_dataloader)
        
        # Update scheduler
        scheduler.step()
        
        # Print metrics
        elapsed = time.time() - start_time
        print(f"Epoch {epoch+1}/{args.epochs}, "
              f"Train Loss: {train_loss:.4f}, "
              f"Val Loss: {val_loss:.4f}, "
              f"Time: {elapsed:.2f}s")
        
        # Save checkpoint if validation loss improved
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'loss_state_dict': loss_fn.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'val_loss': val_loss
            }, os.path.join(args.save_dir, 'best_model.pth'))
            print(f"Saved best model with validation loss: {best_val_loss:.4f}")
    
    print("Training complete!")
    
    # Load best model
    checkpoint = torch.load(os.path.join(args.save_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    loss_fn.load_state_dict(checkpoint['loss_state_dict'])
    
    # Run inference on a sample from the validation set
    model.eval()
    
    # Get a sample from the validation set
    sample = val_dataset[0]
    image = sample['image'].unsqueeze(0).to(device)  # Add batch dimension
    mask = sample['mask'].unsqueeze(0).to(device)    # Add batch dimension
    
    # Run inference
    with torch.no_grad():
        outputs = model(image)
    
    # Visualize results
    visualize_results(
        image=image,
        mask=mask,
        pred_heatmap=outputs['heatmap'],
        instance_labels=outputs.get('instance_labels')
    )


if __name__ == "__main__":
    main()