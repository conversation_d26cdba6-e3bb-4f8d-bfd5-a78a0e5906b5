# Part 1: Setup and Data Loading Instructions

## Overview

This section sets up the environment and prepares the data for spot detection. It supports both 2D and 3D images, as well as real and synthetic data.

## Key Components

1. **Dependencies**: Required packages for spot detection
2. **Data Loading**: Functions to load images and masks from disk
3. **Synthetic Data Generation**: Creation of synthetic spots with varying sizes and densities
4. **Data Augmentation**: Comprehensive augmentation pipeline
5. **Sparse Annotation Handling**: Support for confidence masks and partial annotations

## Working with 2D Images

For 2D images (default configuration):

```python
# Configuration for 2D images
DATA_DIR = "/path/to/your/2d/data"
IMAGE_SIZE = 256
BATCH_SIZE = 8
USE_SYNTHETIC = True  # Set to False if you only want to use real data
SYNTHETIC_SIZE = 500  # Number of synthetic samples to generate
AUGMENTATION_LEVEL = 'strong'  # 'none', 'light', 'medium', or 'strong'
```

## Working with 3D Images

For 3D images, make these adjustments:

```python
# Configuration for 3D images
DATA_DIR = "/path/to/your/3d/data"
IMAGE_SIZE = (64, 256, 256)  # (D, H, W) for 3D images
BATCH_SIZE = 2  # Smaller batch size due to memory constraints
USE_SYNTHETIC = False  # 3D synthetic generation requires special handling
AUGMENTATION_LEVEL = 'medium'  # Less aggressive augmentation for 3D
```

You'll also need to modify the `SparseSpotDataset` class to handle 3D data:

```python
# Add this to the SparseSpotDataset class
def _load_3d_data(self):
    """Load 3D images and masks"""
    # Implementation depends on your 3D data format
    # Common formats: TIFF stacks, NIFTI, HDF5
    
    # Example for TIFF stacks:
    from skimage import io
    import os
    
    # Get all directories containing image stacks
    stack_dirs = [d for d in os.listdir(self.data_dir) if os.path.isdir(os.path.join(self.data_dir, d))]
    
    for stack_dir in stack_dirs:
        # Get all TIFF files in the directory
        image_dir = os.path.join(self.data_dir, stack_dir, 'images')
        mask_dir = os.path.join(self.data_dir, stack_dir, 'masks')
        
        if os.path.exists(image_dir) and os.path.exists(mask_dir):
            # Load image stack
            image_files = sorted([f for f in os.listdir(image_dir) if f.endswith('.tif')])
            image_stack = np.stack([io.imread(os.path.join(image_dir, f)) for f in image_files])
            
            # Load mask stack
            mask_files = sorted([f for f in os.listdir(mask_dir) if f.endswith('.tif')])
            mask_stack = np.stack([io.imread(os.path.join(mask_dir, f)) for f in mask_files])
            
            # Add to dataset
            self.images.append(image_stack)
            self.masks.append(mask_stack)
```

## Handling Sparse Annotations

For datasets with sparse annotations:

1. **Confidence Masks**: Create confidence masks with the same dimensions as your masks, where values range from 0 (no confidence) to 1 (full confidence)
2. **Naming Convention**: Save confidence masks with `_conf` suffix (e.g., `image1_mask.png` and `image1_conf.png`)
3. **Default Behavior**: If no confidence mask is found, full confidence (1.0) is assumed for all annotated regions

Example of creating a confidence mask:

```python
# Example: Create a confidence mask where annotations near the border are less confident
def create_confidence_mask(mask, border_width=10):
    # Start with full confidence
    conf_mask = np.ones_like(mask, dtype=np.float32)
    
    # Reduce confidence near the border
    h, w = mask.shape
    for i in range(border_width):
        # Create border mask
        border = np.zeros_like(mask, dtype=bool)
        border[:i, :] = True  # Top
        border[h-i:, :] = True  # Bottom
        border[:, :i] = True  # Left
        border[:, w-i:] = True  # Right
        
        # Reduce confidence in border
        conf_factor = i / border_width
        conf_mask[border & (mask > 0)] = conf_factor
    
    return conf_mask
```

## Data Augmentation Levels

The notebook supports multiple levels of augmentation:

1. **None**: No augmentation, just resizing
2. **Light**: Basic flips and rotations
3. **Medium**: Adds geometric transformations and intensity changes
4. **Strong**: Adds elastic deformations and advanced augmentations

For 3D data, use 'light' or 'medium' augmentation to avoid excessive memory usage.

## Synthetic Data Generation

Synthetic data generation helps with:

1. **Balancing the Dataset**: Add more examples of underrepresented spot types
2. **Dense Regions**: Create challenging scenarios with overlapping spots
3. **Varying Sizes**: Generate spots of different sizes for better generalization

Adjust synthetic data parameters based on your real data characteristics:

```python
# Adjust synthetic data parameters
train_dataset = SparseSpotDataset(
    # ... other parameters ...
    synthetic=True,
    synthetic_size=500,
    min_spots=5,
    max_spots=20,
    min_radius=2,
    max_radius=10,
    dense_regions_prob=0.3,
    noise_level=0.1
)
```

## Next Steps

After setting up the data, proceed to Part 2 to implement the model and loss function.